'use client'

import React, { useState } from 'react'
import { useWaitlist } from '@/hooks/useWaitlist'

export default function WaitlistForm() {
  const { joinWaitlist, loading, error, response } = useWaitlist()
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    company: '',
    role: '',
    interests: [] as string[]
  })

  const interestOptions = [
    'API Access',
    'Automation',
    'Analytics',
    'Integration',
    'Custom Solutions'
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      await joinWaitlist(formData)
    } catch (err) {
      // Error is already handled in the hook
    }
  }

  const handleInterestToggle = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }))
  }

  const copyReferralLink = () => {
    if (response?.referral_link) {
      navigator.clipboard.writeText(response.referral_link)
      alert('Referral link copied!')
    }
  }

  // Show success state
  if (response?.success) {
    return (
      <div className="max-w-md mx-auto p-6 bg-green-50 rounded-lg">
        <h2 className="text-2xl font-bold text-green-800 mb-4">
          🎉 You're on the list!
        </h2>
        <p className="text-gray-700 mb-4">
          You're currently <strong>#{response.position}</strong> on our waitlist.
        </p>
        
        <div className="bg-white p-4 rounded-md mb-4">
          <p className="text-sm text-gray-600 mb-2">
            Move up the waitlist by referring friends:
          </p>
          <div className="flex gap-2">
            <input
              type="text"
              value={response.referral_link || ''}
              readOnly
              className="flex-1 px-3 py-2 border rounded-md text-sm"
            />
            <button
              onClick={copyReferralLink}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Copy
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Get 3 spots ahead for each friend who joins!
          </p>
        </div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="max-w-md mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Join the Waitlist</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Email *
          </label>
          <input
            type="email"
            required
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="<EMAIL>"
            data-track-click="email_input"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            Name
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="John Doe"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            Company
          </label>
          <input
            type="text"
            value={formData.company}
            onChange={(e) => setFormData({ ...formData, company: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="Acme Inc"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            Role
          </label>
          <select
            value={formData.role}
            onChange={(e) => setFormData({ ...formData, role: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
          >
            <option value="">Select your role</option>
            <option value="founder">Founder/CEO</option>
            <option value="developer">Developer</option>
            <option value="designer">Designer</option>
            <option value="marketer">Marketer</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">
            What are you interested in?
          </label>
          <div className="space-y-2">
            {interestOptions.map((interest) => (
              <label key={interest} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.interests.includes(interest)}
                  onChange={() => handleInterestToggle(interest)}
                  className="mr-2"
                />
                <span className="text-sm">{interest}</span>
              </label>
            ))}
          </div>
        </div>
        
        <button
          type="submit"
          disabled={loading}
          className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
          data-track-click="submit_waitlist"
        >
          {loading ? 'Joining...' : 'Join Waitlist'}
        </button>
      </div>
    </form>
  )
}