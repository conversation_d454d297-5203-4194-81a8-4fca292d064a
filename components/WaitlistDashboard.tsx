'use client'

import React, { useEffect, useState } from 'react'

interface WaitlistStats {
  overview: {
    total_users: number
    waiting_users: number
    converted_users: number
    conversion_rate: string
  }
  engagement: Record<string, number>
  growth: Record<string, number>
}

export default function WaitlistDashboard() {
  const [stats, setStats] = useState<WaitlistStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/waitlist/analytics')
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <div className="p-8 text-center">Loading analytics...</div>
  }

  if (!stats) {
    return <div className="p-8 text-center">Failed to load analytics</div>
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Waitlist Analytics</h1>
      
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Total Users</h3>
          <p className="text-3xl font-bold">{stats.overview.total_users}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Waiting</h3>
          <p className="text-3xl font-bold">{stats.overview.waiting_users}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Converted</h3>
          <p className="text-3xl font-bold">{stats.overview.converted_users}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm">Conversion Rate</h3>
          <p className="text-3xl font-bold">{stats.overview.conversion_rate}%</p>
        </div>
      </div>
      
      {/* Engagement Events */}
      <div className="bg-white p-6 rounded-lg shadow mb-8">
        <h2 className="text-xl font-bold mb-4">Engagement (Last 7 Days)</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(stats.engagement || {}).map(([event, count]) => (
            <div key={event} className="text-center">
              <p className="text-gray-500 text-sm">{event.replace(/_/g, ' ')}</p>
              <p className="text-2xl font-semibold">{count}</p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Growth Chart (simplified) */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">Daily Signups (Last 30 Days)</h2>
        <div className="space-y-2">
          {Object.entries(stats.growth || {})
            .slice(-10)
            .map(([date, count]) => (
              <div key={date} className="flex items-center gap-4">
                <span className="text-sm text-gray-500 w-24">{date}</span>
                <div className="flex-1 bg-gray-200 rounded-full h-6">
                  <div
                    className="bg-blue-600 h-6 rounded-full flex items-center justify-end pr-2"
                    style={{ width: `${(count / Math.max(...Object.values(stats.growth))) * 100}%` }}
                  >
                    <span className="text-white text-xs">{count}</span>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}