import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { headers } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      userId,
      eventType,
      eventData = {},
      sessionId,
      pageUrl,
      referrerUrl
    } = body

    // Get user IP and user agent
    const headersList = headers()
    const ip_address = headersList.get('x-forwarded-for') || headersList.get('x-real-ip')
    const user_agent = headersList.get('user-agent')

    // If no userId, try to identify by email or session
    let user_id = userId
    if (!user_id && eventData.email) {
      const { data: user } = await supabaseAdmin
        .from('waitlist_users')
        .select('id')
        .eq('email', eventData.email)
        .single()
      
      if (user) {
        user_id = user.id
      }
    }

    // Track the event
    const { data: event, error } = await supabaseAdmin.rpc('track_engagement', {
      p_user_id: user_id,
      p_event_type: eventType,
      p_event_data: eventData,
      p_session_id: sessionId,
      p_page_url: pageUrl || headersList.get('referer'),
      p_ip_address: ip_address
    })

    if (error) {
      console.error('Tracking error:', error)
      return NextResponse.json(
        { error: 'Failed to track event' },
        { status: 500 }
      )
    }

    // Handle specific event types
    switch (eventType) {
      case 'email_open':
        await supabaseAdmin
          .from('email_events')
          .update({ opened_at: new Date().toISOString() })
          .eq('id', eventData.emailId)
        break
      
      case 'email_click':
        await supabaseAdmin
          .from('email_events')
          .update({ clicked_at: new Date().toISOString() })
          .eq('id', eventData.emailId)
        break
      
      case 'referral_click':
        // Track referral link clicks even for non-users
        if (eventData.referralCode) {
          const { data: referrer } = await supabaseAdmin
            .from('waitlist_users')
            .select('id')
            .eq('referral_code', eventData.referralCode)
            .single()
          
          if (referrer) {
            await supabaseAdmin.rpc('track_engagement', {
              p_user_id: referrer.id,
              p_event_type: 'referral_link_clicked',
              p_event_data: { 
                ...eventData,
                clicked_by_ip: ip_address 
              },
              p_ip_address: ip_address
            })
          }
        }
        break
    }

    return NextResponse.json({
      success: true,
      eventId: event
    })

  } catch (error) {
    console.error('Tracking error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint for email open tracking (using image pixel)
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const emailId = searchParams.get('eid')
  const userId = searchParams.get('uid')

  if (emailId) {
    // Update email as opened
    await supabaseAdmin
      .from('email_events')
      .update({ opened_at: new Date().toISOString() })
      .eq('id', emailId)

    // Track engagement
    if (userId) {
      await supabaseAdmin.rpc('track_engagement', {
        p_user_id: userId,
        p_event_type: 'email_open',
        p_event_data: { email_id: emailId }
      })
    }
  }

  // Return 1x1 transparent pixel
  const pixel = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    'base64'
  )

  return new NextResponse(pixel, {
    headers: {
      'Content-Type': 'image/png',
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}