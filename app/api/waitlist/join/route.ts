import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { headers } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      email, 
      name, 
      company, 
      role, 
      interests,
      referralCode,
      source = 'website',
      utm_source,
      utm_medium,
      utm_campaign
    } = body

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email || !emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      )
    }

    // Get user IP and user agent
    const headersList = headers()
    const ip_address = headersList.get('x-forwarded-for') || headersList.get('x-real-ip')
    const user_agent = headersList.get('user-agent')

    // Check if email already exists
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from('waitlist_users')
      .select('id, position, referral_code')
      .eq('email', email)
      .single()

    if (existingUser) {
      // Track re-engagement
      await supabaseAdmin.rpc('track_engagement', {
        p_user_id: existingUser.id,
        p_event_type: 'waitlist_revisit',
        p_event_data: { source, utm_source, utm_medium, utm_campaign },
        p_page_url: headersList.get('referer'),
        p_ip_address: ip_address
      })

      return NextResponse.json({
        success: true,
        position: existingUser.position,
        referral_code: existingUser.referral_code,
        message: 'You are already on the waitlist!'
      })
    }

    // Get referrer if referral code provided
    let referred_by = null
    if (referralCode) {
      const { data: referrer } = await supabaseAdmin
        .from('waitlist_users')
        .select('id')
        .eq('referral_code', referralCode)
        .single()
      
      if (referrer) {
        referred_by = referrer.id
      }
    }

    // Create new waitlist entry
    const { data: newUser, error: insertError } = await supabaseAdmin
      .from('waitlist_users')
      .insert({
        email,
        name,
        company,
        role,
        interests,
        source,
        utm_source,
        utm_medium,
        utm_campaign,
        referred_by,
        ip_address,
        user_agent,
        metadata: {
          landing_page: headersList.get('referer'),
          signup_time: new Date().toISOString()
        }
      })
      .select('id, position, referral_code')
      .single()

    if (insertError) {
      console.error('Insert error:', insertError)
      return NextResponse.json(
        { error: 'Failed to join waitlist' },
        { status: 500 }
      )
    }

    // Track signup event
    await supabaseAdmin.rpc('track_engagement', {
      p_user_id: newUser.id,
      p_event_type: 'waitlist_signup',
      p_event_data: { 
        source, 
        utm_source, 
        utm_medium, 
        utm_campaign,
        referred_by: referred_by || null
      },
      p_page_url: headersList.get('referer'),
      p_ip_address: ip_address
    })

    // If referred, create referral conversion record
    if (referred_by) {
      await supabaseAdmin
        .from('referral_conversions')
        .insert({
          referrer_id: referred_by,
          referred_id: newUser.id
        })

      // Optionally move referrer up the waitlist
      const { data: referrerData } = await supabaseAdmin
        .from('waitlist_users')
        .select('position')
        .eq('id', referred_by)
        .single()

      if (referrerData && referrerData.position > 1) {
        const newPosition = Math.max(1, referrerData.position - 3) // Move up 3 spots
        await supabaseAdmin
          .from('waitlist_users')
          .update({ position: newPosition })
          .eq('id', referred_by)
      }
    }

    return NextResponse.json({
      success: true,
      position: newUser.position,
      referral_code: newUser.referral_code,
      referral_link: `${process.env.NEXT_PUBLIC_BASE_URL}?ref=${newUser.referral_code}`,
      message: 'Successfully joined the waitlist!'
    })

  } catch (error) {
    console.error('Waitlist error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}