import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Get basic waitlist stats
    const { data: stats, error: statsError } = await supabaseAdmin
      .from('waitlist_analytics')
      .select('*')
      .order('signup_date', { ascending: false })
      .limit(30)

    if (statsError) throw statsError

    // Get total counts
    const { count: totalUsers } = await supabaseAdmin
      .from('waitlist_users')
      .select('*', { count: 'exact', head: true })

    const { count: waitingUsers } = await supabaseAdmin
      .from('waitlist_users')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'waiting')

    const { count: convertedUsers } = await supabaseAdmin
      .from('waitlist_users')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'converted')

    // Get referral stats
    const { data: topReferrers } = await supabaseAdmin
      .from('referral_conversions')
      .select(`
        referrer_id,
        waitlist_users!referrer_id (
          email,
          name,
          position
        )
      `)
      .limit(10)

    // Get engagement stats
    const { data: engagementStats } = await supabaseAdmin
      .from('engagement_events')
      .select('event_type')
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

    // Process engagement data
    const engagementCounts = engagementStats?.reduce((acc, event) => {
      acc[event.event_type] = (acc[event.event_type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Get conversion funnel
    const { data: funnelData } = await supabaseAdmin.rpc('get_conversion_funnel')

    // Get growth rate
    const { data: growthData } = await supabaseAdmin
      .from('waitlist_users')
      .select('created_at')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: true })

    // Calculate daily growth
    const dailyGrowth = growthData?.reduce((acc, user) => {
      const date = new Date(user.created_at).toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      overview: {
        total_users: totalUsers || 0,
        waiting_users: waitingUsers || 0,
        converted_users: convertedUsers || 0,
        conversion_rate: totalUsers ? ((convertedUsers || 0) / totalUsers * 100).toFixed(2) : '0'
      },
      daily_stats: stats,
      referral_stats: {
        top_referrers: topReferrers,
        total_referrals: topReferrers?.length || 0
      },
      engagement: engagementCounts,
      funnel: funnelData,
      growth: dailyGrowth
    })

  } catch (error) {
    console.error('Analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    )
  }
}

// POST endpoint to get specific user analytics
export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email required' },
        { status: 400 }
      )
    }

    // Get user data
    const { data: user, error: userError } = await supabaseAdmin
      .from('waitlist_users')
      .select('*')
      .eq('email', email)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user's referrals
    const { data: referrals } = await supabaseAdmin
      .from('referral_conversions')
      .select(`
        *,
        referred:referred_id (
          email,
          name,
          created_at
        )
      `)
      .eq('referrer_id', user.id)

    // Get user's engagement
    const { data: engagements } = await supabaseAdmin
      .from('engagement_events')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(50)

    // Get email history
    const { data: emails } = await supabaseAdmin
      .from('email_events')
      .select('*')
      .eq('user_id', user.id)
      .order('sent_at', { ascending: false })

    return NextResponse.json({
      user: {
        ...user,
        referrals_count: referrals?.length || 0,
        engagement_score: calculateEngagementScore(engagements || []),
        last_active: engagements?.[0]?.created_at || user.created_at
      },
      referrals,
      engagements,
      emails
    })

  } catch (error) {
    console.error('User analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user analytics' },
      { status: 500 }
    )
  }
}

function calculateEngagementScore(events: any[]): number {
  const weights = {
    page_view: 1,
    form_interaction: 2,
    referral_click: 3,
    referral_link_clicked: 5,
    email_open: 2,
    email_click: 4,
    waitlist_revisit: 3
  }

  const score = events.reduce((total, event) => {
    return total + (weights[event.event_type as keyof typeof weights] || 0)
  }, 0)

  return Math.min(100, score) // Cap at 100
}