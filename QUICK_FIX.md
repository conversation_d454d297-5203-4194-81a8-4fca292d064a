# 🚨 Quick Fix for Email Signup

## The Problem
Your `.env.local` file still has placeholder values. You need to add your REAL Supabase keys!

## Step 1: Get Your Keys (1 minute)

1. Go to: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api
2. You'll see two keys:
   - **anon public** (starts with `eyJ...`)
   - **service_role secret** (also starts with `eyJ...`)

## Step 2: Update .env.local (30 seconds)

Open this file:
```bash
open "/Applications/AI Project /agentised/inspiration/ai-agents-landing/.env.local"
```

Replace these lines:
```
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

With your ACTUAL keys:
```
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ....(your long anon key)
SUPABASE_SERVICE_ROLE_KEY=eyJ....(your long service key)
```

## Step 3: Restart Next.js (30 seconds)

```bash
# Stop the server (Ctrl+C) then:
cd "/Applications/AI Project /agentised/inspiration/ai-agents-landing"
npm run dev
```

## Step 4: Apply Database Tables (1 minute)

1. Go to: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/sql/new
2. Copy ALL the SQL from this file: `/Applications/AI Project /agentised/supabase/migrations/001_waitlist_schema.sql`
3. Paste it in the SQL editor
4. Click "Run"

## That's it! Test it:

1. Go to http://localhost:3000
2. Enter an email
3. Check if it saved: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/editor/waitlist_users

## Still not working?

Check the browser console (F12) and terminal for error messages. Common issues:
- Keys are wrong (double-check you copied the full key)
- Database tables not created (run the SQL migration)
- Next.js needs restart after changing .env.local