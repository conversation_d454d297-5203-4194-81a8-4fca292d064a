# Agentised Startup Guide

## Quick Start

1. **Navigate to the correct directory:**
```bash
cd "/Applications/AI Project /agentised/inspiration/ai-agents-landing"
```

2. **Install dependencies (if not already installed):**
```bash
npm install
```

3. **Create environment file:**
```bash
cp env.example .env.local
```

4. **Update .env.local with your Supabase credentials:**
```
NEXT_PUBLIC_SUPABASE_URL=https://uqohigtauhgygiyplftk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
ADMIN_KEY=your-secure-admin-key-here
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

Get these values from: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api

5. **Run the development server:**
```bash
npm run dev
```

6. **Open in browser:**
Navigate to http://localhost:3000

## Complete Terminal Commands

Copy and paste these commands in order:

```bash
# Navigate to project
cd "/Applications/AI Project /agentised/inspiration/ai-agents-landing"

# Install dependencies
npm install

# Create environment file
cp env.example .env.local

# Open environment file to add your keys
open .env.local

# Start development server
npm run dev
```

## Troubleshooting

### If npm install fails:
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### If port 3000 is busy:
```bash
# Run on different port
PORT=3001 npm run dev
```

### If WebSocket server is needed:
```bash
# In a separate terminal
node websocket-server.js
```

## Testing the Waitlist

1. **Basic signup:**
   - Go to http://localhost:3000
   - Enter email and submit

2. **Test referral link:**
   - Visit http://localhost:3000?ref=TEST1234
   - Sign up and check if referral is tracked

3. **Check waitlist status:**
   - Use the API endpoint or create a status check page

## Mobile Testing

For mobile testing on the same network:
```bash
# Make it accessible on your network
npm run dev -- --hostname 0.0.0.0
```

Then access via your computer's IP address (e.g., http://*************:3000)