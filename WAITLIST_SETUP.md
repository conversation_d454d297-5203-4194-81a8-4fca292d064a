# Waitlist System Setup Guide

## Overview
This waitlist system is built with Supabase and includes:
- Email collection with progressive profiling
- Referral system (move up 3 spots per referral)
- Comprehensive engagement tracking
- Analytics dashboard
- Personalization-ready infrastructure

## Setup Instructions

### 1. Supabase Setup

1. Create a new Supabase project at https://supabase.com
2. Go to SQL Editor and run the migration script:
   ```
   /supabase/migrations/001_waitlist_schema.sql
   ```

3. Get your API keys from Settings > API:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY` 
   - `SUPABASE_SERVICE_KEY` (for server-side operations)

### 2. Environment Variables

Copy `env.example` to `.env.local` and fill in your values:
```bash
cp env.example .env.local
```

### 3. Install Dependencies

```bash
npm install @supabase/supabase-js uuid
```

## Usage

### Basic Waitlist Form
```tsx
import WaitlistForm from '@/components/WaitlistForm'

export default function HomePage() {
  return <WaitlistForm />
}
```

### Track User Engagement
The system automatically tracks:
- Page views
- Form interactions
- Scroll depth
- Time on page
- Referral clicks
- Email opens/clicks

### Referral System
Users get a unique referral link after signing up. Each successful referral moves them up 3 spots in the waitlist.

### Analytics Dashboard
```tsx
import WaitlistDashboard from '@/components/WaitlistDashboard'

// Protected route - add authentication
export default function AdminDashboard() {
  return <WaitlistDashboard />
}
```

## API Endpoints

### Join Waitlist
```
POST /api/waitlist/join
Body: {
  email: string,
  name?: string,
  company?: string,
  role?: string,
  interests?: string[],
  referralCode?: string
}
```

### Track Event
```
POST /api/track
Body: {
  eventType: string,
  eventData: object,
  userId?: string
}
```

### Get Analytics
```
GET /api/waitlist/analytics
```

## Engagement Tracking Events

The system tracks these events automatically:
- `page_view` - When a page loads
- `form_interaction` - Form started/completed/abandoned
- `scroll_depth` - 25%, 50%, 75%, 100%
- `referral_click` - When someone clicks a referral link
- `waitlist_signup` - Successful signup
- `email_open` - Email opened (via tracking pixel)
- `email_click` - Link clicked in email

## Database Schema

### Main Tables:
- `waitlist_users` - User data and metadata
- `engagement_events` - All user interactions
- `email_events` - Email campaign tracking
- `referral_conversions` - Referral relationships

## Next Steps for Personalized Emails

1. **Email Service Integration**
   - Set up Resend, SendGrid, or similar
   - Add API keys to environment variables

2. **Email Templates**
   - Create dynamic templates based on user data
   - Use interests, role, and behavior for personalization

3. **AI Personalization**
   - Integrate OpenAI/Anthropic API
   - Generate personalized content based on user profile

4. **Automation Workflows**
   - Welcome series
   - Re-engagement campaigns
   - Milestone celebrations

## Growth Tactics

1. **Referral Incentives**
   - Current: Move up 3 spots per referral
   - Consider: Early access for top referrers
   - Leaderboard for competitive element

2. **Social Proof**
   - Show waitlist position
   - Display total signups
   - Feature testimonials

3. **Engagement Campaigns**
   - Weekly updates on product progress
   - Exclusive content for waitlist members
   - Beta testing opportunities

## Security Considerations

- Row Level Security (RLS) is enabled on all tables
- Use service key only on server-side
- Validate all user inputs
- Rate limit API endpoints
- Monitor for abuse patterns