import { useState, useEffect } from 'react'
import { initializeTracking, trackEvent, trackFormInteraction } from '@/lib/tracking'

interface WaitlistData {
  email: string
  name?: string
  company?: string
  role?: string
  interests?: string[]
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
}

interface WaitlistResponse {
  success: boolean
  position?: number
  referral_code?: string
  referral_link?: string
  message?: string
  error?: string
}

export function useWaitlist() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [response, setResponse] = useState<WaitlistResponse | null>(null)
  
  // Initialize tracking on mount
  useEffect(() => {
    // Check for referral code in URL
    const urlParams = new URLSearchParams(window.location.search)
    const refCode = urlParams.get('ref')
    
    if (refCode) {
      // Track referral click
      trackEvent('referral_click', { referralCode: refCode })
      // Store in session for later use
      sessionStorage.setItem('referral_code', refCode)
    }
    
    // Initialize page tracking
    initializeTracking()
  }, [])
  
  const joinWaitlist = async (data: WaitlistData) => {
    setLoading(true)
    setError(null)
    
    // Track form start
    trackFormInteraction('waitlist_form', 'started')
    
    try {
      // Get referral code from session if exists
      const referralCode = sessionStorage.getItem('referral_code')
      
      // Get UTM parameters
      const urlParams = new URLSearchParams(window.location.search)
      const utmData = {
        utm_source: data.utm_source || urlParams.get('utm_source') || undefined,
        utm_medium: data.utm_medium || urlParams.get('utm_medium') || undefined,
        utm_campaign: data.utm_campaign || urlParams.get('utm_campaign') || undefined,
      }
      
      const response = await fetch('/api/waitlist/join', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          ...utmData,
          referralCode,
          source: window.location.hostname === 'localhost' ? 'development' : 'website'
        }),
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to join waitlist')
      }
      
      setResponse(result)
      
      // Track successful signup
      trackFormInteraction('waitlist_form', 'completed')
      trackEvent('waitlist_success', {
        position: result.position,
        referral_code: result.referral_code
      })
      
      // Clear referral code from session after use
      sessionStorage.removeItem('referral_code')
      
      return result
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Something went wrong'
      setError(errorMessage)
      
      // Track form abandonment
      trackFormInteraction('waitlist_form', 'abandoned')
      trackEvent('waitlist_error', { error: errorMessage })
      
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  const checkPosition = async (email: string) => {
    try {
      const response = await fetch('/api/waitlist/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to check position')
      }
      
      trackEvent('position_check', { email })
      
      return result
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Something went wrong'
      setError(errorMessage)
      throw err
    }
  }
  
  return {
    joinWaitlist,
    checkPosition,
    loading,
    error,
    response,
  }
}

// Hook for tracking user behavior
export function useEngagementTracking(userId?: string) {
  useEffect(() => {
    if (!userId) return
    
    // Track user-specific events
    const trackUserEvent = (eventType: string, eventData?: any) => {
      trackEvent(eventType, eventData, userId)
    }
    
    // Track clicks on important elements
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      
      // Track CTA clicks
      if (target.matches('[data-track-click]')) {
        const trackingData = target.getAttribute('data-track-click')
        trackUserEvent('cta_click', { element: trackingData })
      }
      
      // Track external link clicks
      if (target.matches('a[href^="http"]')) {
        const href = target.getAttribute('href')
        trackUserEvent('external_link_click', { url: href })
      }
    }
    
    document.addEventListener('click', handleClick)
    
    return () => {
      document.removeEventListener('click', handleClick)
    }
  }, [userId])
}