#!/bin/bash

# Waitlist Testing Script

echo "🧪 Testing Agentised Waitlist System..."
echo ""

# Test email signup
echo "📧 Testing email signup..."
curl -X POST http://localhost:3000/api/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "source": "test",
    "utm_source": "test_script",
    "utm_medium": "shell",
    "utm_campaign": "testing"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "---"
echo ""

# Test with referral code
echo "🔗 Testing referral signup..."
curl -X POST http://localhost:3000/api/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "referred_by_code": "TEST1234"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "---"
echo ""

# Test status check
echo "📊 Testing status check..."
curl -X POST http://localhost:3000/api/waitlist/status \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "---"
echo ""

# Test QR code generation
echo "📱 Testing QR code generation..."
curl -X POST http://localhost:3000/api/qr \
  -H "Content-Type: application/json" \
  -d '{
    "campaign": "test_campaign",
    "location": "terminal"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "✅ Testing complete!"
echo ""
echo "Check your Supabase dashboard for results:"
echo "https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/editor"