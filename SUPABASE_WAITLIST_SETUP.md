# Supabase Waitlist Setup Guide

## Project Details
- **Project Name**: agentised
- **Project ID**: uqohigtauhgygiyplftk
- **Region**: eu-west-2
- **Dashboard**: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk

## Setup Steps

### 1. Apply Database Migration

Go to the SQL Editor in your Supabase dashboard and run the migration from:
`/supabase/migrations/001_waitlist_schema.sql`

This creates:
- `waitlist_users` table with referral system
- `engagement_events` for tracking
- `email_events` for email campaign tracking
- `referral_conversions` for referral tracking
- Analytics views and helper functions

### 2. Get API Credentials

From the dashboard Settings > API section, copy:
- `SUPABASE_URL`: https://uqohigtauhgygiyplftk.supabase.co
- `SUPABASE_ANON_KEY`: (public key)
- `SUPABASE_SERVICE_ROLE_KEY`: (service key - keep secret!)

### 3. Set Environment Variables

Create `.env.local` in `/inspiration/ai-agents-landing/`:
```env
NEXT_PUBLIC_SUPABASE_URL=https://uqohigtauhgygiyplftk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
ADMIN_KEY=your-secure-admin-key-here
```

### 4. Deploy Edge Functions

Using Supabase CLI:
```bash
supabase functions deploy waitlist-join
supabase functions deploy waitlist-status
supabase functions deploy waitlist-analytics
```

Or deploy via dashboard by uploading the function files.

### 5. Test the Integration

The waitlist system now supports:

#### Basic Signup
```
https://agentised.com
```

#### Referral Links
```
https://agentised.com?ref=ABCD1234
```

#### UTM Tracking
```
https://agentised.com?utm_source=twitter&utm_medium=social&utm_campaign=launch
```

## API Endpoints

### 1. Join Waitlist
**POST** `/api/subscribe`
```json
{
  "email": "<EMAIL>",
  "source": "website",
  "utm_source": "twitter",
  "utm_medium": "social",
  "utm_campaign": "launch",
  "referred_by_code": "ABCD1234"
}
```

### 2. Check Status
**POST** `/api/waitlist/status`
```json
{
  "email": "<EMAIL>"
}
```

### 3. Generate QR Code
**POST** `/api/qr`
```json
{
  "campaign": "conference_2024",
  "location": "booth_a"
}
```

## Features Implemented

✅ **Email Waitlist**
- Unique email validation
- Position tracking
- Status management (waiting, invited, converted)

✅ **Referral System**
- Auto-generated referral codes
- Referral tracking
- Conversion analytics

✅ **Analytics**
- User growth tracking
- Source attribution
- Engagement events
- Referral performance

✅ **Security**
- Row Level Security (RLS) policies
- Input validation
- Rate limiting ready

## Next Steps

1. **Email Integration**: Set up email service (SendGrid/Resend) for:
   - Welcome emails
   - Position updates
   - Referral notifications

2. **Admin Dashboard**: Build admin interface to:
   - View waitlist analytics
   - Manage user status
   - Export data

3. **Testing**: Run comprehensive tests:
   - API endpoint testing
   - Database constraint testing
   - Performance testing

## Monitoring

Monitor your waitlist at:
- Supabase Dashboard: Table Editor
- Edge Function Logs: Functions > Logs
- Database Metrics: Database > Metrics