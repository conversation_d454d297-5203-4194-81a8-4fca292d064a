// Debug script to test your Supabase connection
const https = require('https');

console.log('🔍 Debugging Waitlist Setup...\n');

// Check environment variables
console.log('1️⃣ Checking environment variables:');
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL || '❌ NOT SET');
console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ SET' : '❌ NOT SET');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ SET' : '❌ NOT SET');

// Test if keys are still placeholders
if (process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY === 'your-anon-key-here') {
  console.log('\n❌ ERROR: You need to replace the placeholder Supabase keys!');
  console.log('\n📝 To fix this:');
  console.log('1. Go to: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api');
  console.log('2. Copy your "anon public" key');
  console.log('3. Copy your "service_role secret" key');
  console.log('4. Update .env.local with these real keys');
  process.exit(1);
}

console.log('\n2️⃣ Testing Supabase connection...');

// Test connection to Supabase
const testConnection = () => {
  const url = new URL(process.env.NEXT_PUBLIC_SUPABASE_URL || '');
  
  https.get({
    hostname: url.hostname,
    path: '/rest/v1/',
    headers: {
      'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''}`
    }
  }, (res) => {
    console.log('Response status:', res.statusCode);
    
    if (res.statusCode === 200 || res.statusCode === 406) {
      console.log('✅ Supabase connection successful!');
    } else {
      console.log('❌ Supabase connection failed');
    }
  }).on('error', (err) => {
    console.log('❌ Connection error:', err.message);
  });
};

if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  testConnection();
} else {
  console.log('❌ Cannot test connection - missing environment variables');
}