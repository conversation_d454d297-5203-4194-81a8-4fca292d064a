# MCP Server Setup for Persistent Configuration

## Using MCP Config File

To make the Supabase MCP server persist across different Claude chats, use the `--mcp-config` flag when starting Claude:

```bash
claude --mcp-config ./claude-mcp-config.json
```

Or with an absolute path:
```bash
claude --mcp-config "/Applications/AI Project /agentised/claude-mcp-config.json"
```

## Alternative: Using Environment Variable

You can also set up an alias in your shell configuration file (`.bashrc`, `.zshrc`, etc.):

```bash
alias claude-supabase='claude --mcp-config "/Applications/AI Project /agentised/claude-mcp-config.json"'
```

Then use:
```bash
claude-supabase
```

## Config File Structure

The `claude-mcp-config.json` file contains:
```json
{
  "mcpServers": {
    "supabase-mcp-server": {
      "transport": "http",
      "url": "https://server.smithery.ai/@supabase-community/supabase-mcp/mcp?api_key=654ed640-8830-4249-983d-c1890c1893da&profile=long-earthworm-u2v2Gs"
    }
  }
}
```

## Adding More MCP Servers

To add additional MCP servers, simply add them to the config file:
```json
{
  "mcpServers": {
    "supabase-mcp-server": { ... },
    "another-server": {
      "transport": "http",
      "url": "https://..."
    }
  }
}
```

## Verifying Configuration

Once you start Claude with the config file, you can verify the MCP servers are loaded:
```bash
claude mcp list
```

This should show all configured MCP servers.