# Apply Supabase Migration

## Option 1: Using Supabase Dashboard
1. Go to https://supabase.com/dashboard/project/uqohigtauhgygiyplftk
2. Navigate to SQL Editor
3. Copy and paste the contents of `/supabase/migrations/001_waitlist_schema.sql`
4. Click "Run"

## Option 2: Using Supabase CLI
```bash
# Install Supabase CLI if you haven't already
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref uqohigtauhgygiyplftk

# Apply the migration
supabase db push
```

## Get Service Role Key
1. Go to https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api
2. Under "Project API keys", find the "service_role" key (keep this secret!)
3. Copy it and update the SUPABASE_SERVICE_ROLE_KEY in .env.local