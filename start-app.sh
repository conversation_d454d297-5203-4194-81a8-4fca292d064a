#!/bin/bash

# Agentised Quick Start Script

echo "🚀 Starting Agentised setup..."

# Navigate to the correct directory
cd "/Applications/AI Project /agentised/inspiration/ai-agents-landing"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "✅ Dependencies already installed"
fi

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "🔧 Creating .env.local file..."
    cp env.example .env.local
    echo "⚠️  Please update .env.local with your Supabase credentials:"
    echo "   Get them from: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api"
    echo ""
    echo "Press Enter after updating .env.local to continue..."
    read -r
else
    echo "✅ .env.local already exists"
fi

# Start the development server
echo "🌟 Starting development server..."
npm run dev