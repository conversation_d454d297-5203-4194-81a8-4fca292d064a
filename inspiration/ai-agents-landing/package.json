{"name": "ai-agents-landing", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "dev:mobile": "./start-mobile.sh", "websocket": "node websocket-server.js", "dev:all": "concurrently \"npm run dev\" \"npm run websocket\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@supabase/supabase-js": "^2.50.3", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "child_process": "^1.0.2", "concurrently": "^9.2.0", "framer-motion": "^12.23.0", "next": "^15.3.5", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}