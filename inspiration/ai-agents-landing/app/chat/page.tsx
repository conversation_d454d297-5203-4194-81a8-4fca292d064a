'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import io, { Socket } from 'socket.io-client';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  typing?: boolean;
  tool?: string;
  agentType?: string;
}

const AGENT_TYPES = [
  { id: 'customer-support', name: 'Customer Support', icon: '💬' },
  { id: 'sales-automation', name: 'Sales Automation', icon: '📈' },
  { id: 'content-creation', name: 'Content Creation', icon: '✍️' },
  { id: 'data-analysis', name: 'Data Analysis', icon: '📊' }
];

export default function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Welcome! I\'m your AI Agent Builder. What kind of agent would you like to create today?',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [isBuilding, setIsBuilding] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    // Initialize WebSocket
    const socketInstance = io('http://localhost:3001');
    setSocket(socketInstance);
    setSessionId(`session_${Date.now()}`);

    socketInstance.on('connect', () => {
      console.log('Connected to agent builder');
    });

    socketInstance.on('agent-output', (data) => {
      if (!isBuilding) return;
      
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        role: 'assistant',
        content: data.output,
        timestamp: new Date()
      }]);
    });

    socketInstance.on('agent-thinking', (data) => {
      if (!isBuilding) return;
      
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        role: 'system',
        content: `💭 ${data.thought}`,
        timestamp: new Date()
      }]);
    });

    socketInstance.on('tool-execution', (data) => {
      if (!isBuilding) return;
      
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        role: 'system',
        content: `🔧 Executing: ${data.toolName}`,
        tool: data.toolName,
        timestamp: new Date()
      }]);
    });

    socketInstance.on('build-progress', (data) => {
      if (!isBuilding) return;
      
      if (data.status === 'completed') {
        setIsBuilding(false);
        setMessages(prev => [...prev, {
          id: Date.now().toString(),
          role: 'assistant',
          content: '✅ Your agent has been created successfully! What would you like to do next?',
          timestamp: new Date()
        }]);
      }
    });

    return () => {
      socketInstance.disconnect();
    };
  }, [isBuilding]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsTyping(true);

    // Simulate AI thinking
    setTimeout(() => {
      if (!selectedAgent) {
        // Check if user is asking to create a specific type of agent
        const agentType = AGENT_TYPES.find(agent => 
          input.toLowerCase().includes(agent.name.toLowerCase())
        );

        if (agentType) {
          setSelectedAgent(agentType.id);
          setMessages(prev => [...prev, {
            id: Date.now().toString(),
            role: 'assistant',
            content: `Great choice! I'll help you create a ${agentType.name} agent. ${agentType.icon}\n\nWhat specific tasks should this agent handle? For example:\n${getAgentExample(agentType.id)}`,
            timestamp: new Date(),
            agentType: agentType.id
          }]);
        } else {
          // Show agent options
          setMessages(prev => [...prev, {
            id: Date.now().toString(),
            role: 'assistant',
            content: 'I can help you create these types of agents:',
            timestamp: new Date()
          }]);
          
          // Add agent cards
          setTimeout(() => {
            setMessages(prev => [...prev, {
              id: 'agent-options',
              role: 'system',
              content: 'agent-cards',
              timestamp: new Date()
            }]);
          }, 300);
        }
      } else {
        // Start building the agent
        startBuildingAgent(input);
      }
      setIsTyping(false);
    }, 1000);
  };

  const startBuildingAgent = (prompt: string) => {
    setIsBuilding(true);
    
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      role: 'assistant',
      content: `🚀 Starting to build your ${AGENT_TYPES.find(a => a.id === selectedAgent)?.name} agent...\n\nI'll analyze your requirements and create the agent for you.`,
      timestamp: new Date()
    }]);

    // Emit to WebSocket
    socket?.emit('start-agent', {
      sessionId,
      prompt,
      template: selectedAgent
    });
  };

  const getAgentExample = (agentType: string) => {
    const examples: { [key: string]: string } = {
      'customer-support': '• Answer product questions\n• Process returns and refunds\n• Escalate complex issues\n• Provide 24/7 support',
      'sales-automation': '• Qualify incoming leads\n• Schedule demo calls\n• Send follow-up emails\n• Update CRM automatically',
      'content-creation': '• Write blog posts about industry trends\n• Create social media content\n• Generate email newsletters\n• Optimize for SEO',
      'data-analysis': '• Track key business metrics\n• Generate weekly reports\n• Identify trends and patterns\n• Alert on anomalies'
    };
    return examples[agentType] || '';
  };

  const selectAgentType = (agentId: string) => {
    setSelectedAgent(agentId);
    const agent = AGENT_TYPES.find(a => a.id === agentId);
    
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      role: 'assistant',
      content: `Great! Let's create a ${agent?.name} agent. ${agent?.icon}\n\nTell me what specific tasks you want this agent to handle. The more details you provide, the better I can customize it for your needs.`,
      timestamp: new Date()
    }]);
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <header className="border-b border-white/10 backdrop-blur-sm bg-black/80 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-xl font-bold">AI Agent Builder</span>
            </Link>
            <div className="flex items-center space-x-4">
              {selectedAgent && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center space-x-2 px-3 py-1 bg-white/10 rounded-full"
                >
                  <span>{AGENT_TYPES.find(a => a.id === selectedAgent)?.icon}</span>
                  <span className="text-sm">{AGENT_TYPES.find(a => a.id === selectedAgent)?.name}</span>
                </motion.div>
              )}
              <button
                onClick={() => window.location.reload()}
                className="text-sm text-white/60 hover:text-white transition-colors"
              >
                New Chat
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-8">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="mb-6"
              >
                {message.role === 'user' && (
                  <div className="flex justify-end">
                    <div className="max-w-[80%] bg-white/10 rounded-2xl px-4 py-3">
                      <p className="text-white whitespace-pre-wrap">{message.content}</p>
                    </div>
                  </div>
                )}

                {message.role === 'assistant' && (
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-xs">AI</span>
                    </div>
                    <div className="max-w-[80%]">
                      <p className="text-white/90 whitespace-pre-wrap">{message.content}</p>
                    </div>
                  </div>
                )}

                {message.role === 'system' && message.content === 'agent-cards' && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4">
                    {AGENT_TYPES.map((agent) => (
                      <motion.button
                        key={agent.id}
                        onClick={() => selectAgentType(agent.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="p-4 bg-white/5 border border-white/20 rounded-lg hover:border-white/40 transition-all text-left"
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{agent.icon}</span>
                          <div>
                            <h3 className="font-medium">{agent.name}</h3>
                            <p className="text-sm text-white/60 mt-1">Click to create this agent</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                )}

                {message.role === 'system' && message.content !== 'agent-cards' && (
                  <div className="flex items-start space-x-3">
                    <div className="max-w-[80%] bg-black/50 border border-white/10 rounded-lg px-3 py-2">
                      <p className="text-white/60 text-sm font-mono">{message.content}</p>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}

            {isTyping && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex items-start space-x-3 mb-6"
              >
                <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                  <span className="text-xs">AI</span>
                </div>
                <div className="flex space-x-1">
                  <motion.div
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="w-2 h-2 bg-white/60 rounded-full"
                  />
                  <motion.div
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
                    className="w-2 h-2 bg-white/60 rounded-full"
                  />
                  <motion.div
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
                    className="w-2 h-2 bg-white/60 rounded-full"
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className="border-t border-white/10 bg-black/80 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4">
          <div className="flex items-end space-x-3">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              placeholder={isBuilding ? "Agent is building..." : "Describe what your agent should do..."}
              disabled={isBuilding}
              className="flex-1 bg-white/5 border border-white/20 rounded-lg px-4 py-3 focus:outline-none focus:border-white/40 resize-none min-h-[60px] max-h-[200px] disabled:opacity-50"
              rows={1}
            />
            <button
              onClick={handleSend}
              disabled={!input.trim() || isBuilding}
              className="px-6 py-3 bg-white text-black rounded-lg font-medium hover:bg-white/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}