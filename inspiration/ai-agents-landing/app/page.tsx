'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'

export default function Home() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [currentCapability, setCurrentCapability] = useState(0)
  const [activeFlow, setActiveFlow] = useState(-1)
  const [terminalText, setTerminalText] = useState('')
  const [selectedAgent, setSelectedAgent] = useState(0)
  const [isTyping, setIsTyping] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [withoutCodeText, setWithoutCodeText] = useState('')
  
  const containerRef = useRef<HTMLDivElement>(null)
  const terminalRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  })
  
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.5], [1, 0.8])

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Animate "Without Code" text
  useEffect(() => {
    const text = 'Without Code'
    let currentIndex = 0
    let typeInterval: NodeJS.Timeout
    
    const startTyping = setTimeout(() => {
      typeInterval = setInterval(() => {
        if (currentIndex <= text.length) {
          setWithoutCodeText(text.slice(0, currentIndex))
          currentIndex++
        } else {
          clearInterval(typeInterval)
        }
      }, 100)
    }, 500) // Start typing after 500ms
    
    return () => {
      clearTimeout(startTyping)
      if (typeInterval) clearInterval(typeInterval)
    }
  }, [])

  const capabilities = [
    { 
      title: "Customer Support",
      flow: ["User Request", "AI Analysis", "Auto Response", "Happy Customer"],
      metric: "95% Faster Response"
    },
    { 
      title: "Sales Automation",
      flow: ["Lead Capture", "AI Qualification", "Personalized Outreach", "Closed Deal"],
      metric: "3x More Conversions"
    },
    { 
      title: "Content Creation",
      flow: ["Content Brief", "AI Generation", "Human Polish", "Published Asset"],
      metric: "10x Content Output"
    },
    { 
      title: "Data Analysis",
      flow: ["Raw Data", "AI Processing", "Insights Generated", "Decision Made"],
      metric: "Real-time Insights"
    }
  ]

  const agentExamples = [
    {
      name: "Customer Support",
      input: "I can't login to my account",
      process: [
        "> Agent: Analyzing user issue...",
        "> Agent: Checking account status...",
        "> Agent: Found: Password reset needed",
        "> Agent: Sending reset instructions..."
      ],
      output: "Reset link <NAME_EMAIL>. Issue resolved in 12 seconds."
    },
    {
      name: "Data Analysis",
      input: "Analyze Q4 sales performance",
      process: [
        "> Agent: Loading sales data...",
        "> Agent: Processing 15,423 transactions...",
        "> Agent: Identifying trends...",
        "> Agent: Generating insights..."
      ],
      output: "Q4 sales up 23% YoY. Top product: AI Suite. Key region: North America."
    },
    {
      name: "Content Creation",
      input: "Write a blog post about AI trends",
      process: [
        "> Agent: Researching latest AI developments...",
        "> Agent: Analyzing top trends...",
        "> Agent: Generating content structure...",
        "> Agent: Writing article..."
      ],
      output: "1,500-word blog post created: '5 AI Trends Reshaping Business in 2025'"
    }
  ]

  // Rotate through capabilities
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentCapability((prev) => (prev + 1) % capabilities.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [capabilities.length])

  // Animate flow
  useEffect(() => {
    if (activeFlow >= 0 && activeFlow < 3) {
      const timeout = setTimeout(() => {
        setActiveFlow(prev => prev + 1)
      }, 1000) // Time between each step
      return () => clearTimeout(timeout)
    }
  }, [activeFlow, currentCapability])

  // Reset activeFlow when capability changes
  useEffect(() => {
    setActiveFlow(-1) // Start before first node
    // Small delay then start animation
    const startTimeout = setTimeout(() => {
      setActiveFlow(0)
    }, 200)
    return () => clearTimeout(startTimeout)
  }, [currentCapability])

  // Typewriter effect
  const typewriterEffect = async (text: string, startIndex: number = 0) => {
    setIsTyping(true)
    let currentText = text.substring(0, startIndex)
    
    for (let i = startIndex; i < text.length; i++) {
      currentText = text.substring(0, i + 1)
      setTerminalText(currentText)
      await new Promise(resolve => setTimeout(resolve, 30))
    }
    
    setIsTyping(false)
    return currentText
  }

  // Auto-cycle through terminal examples
  useEffect(() => {
    const cycleInterval = setInterval(() => {
      setSelectedAgent((prev) => (prev + 1) % agentExamples.length)
    }, 8000) // Change every 8 seconds
    
    return () => clearInterval(cycleInterval)
  }, [agentExamples.length])

  // Animate terminal when agent changes
  useEffect(() => {
    let isCancelled = false
    
    const animateTerminal = async () => {
      const example = agentExamples[selectedAgent]
      
      // Clear terminal
      setTerminalText('')
      if (isCancelled) return
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // Build the full terminal text
      let fullText = `> User: "${example.input}"\n`
      
      // Type input
      if (!isCancelled) {
        await typewriterEffect(fullText)
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      
      // Add and type process lines
      for (const line of example.process) {
        if (isCancelled) return
        fullText += line + '\n'
        await typewriterEffect(fullText, fullText.length - line.length - 1)
        await new Promise(resolve => setTimeout(resolve, 300))
      }
      
      if (isCancelled) return
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Add and type output
      fullText += `> Result: ${example.output}`
      if (!isCancelled) {
        await typewriterEffect(fullText, fullText.length - example.output.length - 10)
      }
    }
    
    animateTerminal()
    
    return () => {
      isCancelled = true
    }
  }, [selectedAgent])

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    
    if (!validateEmail(email)) {
      setError('Please enter a valid email address')
      return
    }

    setLoading(true)
    
    try {
      // Get URL parameters for tracking
      const urlParams = new URLSearchParams(window.location.search)
      
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email,
          source: 'website',
          utm_source: urlParams.get('utm_source'),
          utm_medium: urlParams.get('utm_medium'),
          utm_campaign: urlParams.get('utm_campaign'),
          referred_by_code: urlParams.get('ref')
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong')
      }

      setSuccess(true)
      setEmail('')
      
      // Store referral code for sharing
      if (data.referral_code) {
        localStorage.setItem('waitlist_referral_code', data.referral_code)
      }
      
      // Redirect to build interface after 2 seconds
      setTimeout(() => {
        window.location.href = '/build'
      }, 2000)
      
    } catch (err) {
      setError('Something went wrong. Please try again.')
      setLoading(false)
    }
  }

  return (
    <main className="min-h-screen bg-black text-white relative">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-black to-gray-950" />
        {!isMobile && (
          <motion.div 
            animate={{ 
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{ 
              duration: 20, 
              repeat: Infinity,
              ease: "linear" 
            }}
            className="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] opacity-20"
          >
            <div className="absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent" />
          </motion.div>
        )}
        {/* Subtle grid */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:100px_100px]" />
      </div>

      {/* Header with Demo Button */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="text-xl font-bold">AI Agents</div>
            <a
              href="/build"
              className="px-4 py-2 bg-white text-black rounded-lg font-medium hover:bg-white/90 transition-colors text-sm"
            >
              Start Building
            </a>
          </div>
        </div>
      </header>

      {/* Hero Section with Workflow */}
      <section ref={containerRef} className="relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20">
        <motion.div 
          style={{ opacity, scale }}
          className="w-full max-w-6xl mx-auto"
        >
          {/* Main Heading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="text-center mb-20"
          >
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tighter mb-6">
              Build AI Agents
              <span className="block text-3xl sm:text-4xl md:text-5xl lg:text-6xl mt-4 h-[1.2em]">
                {withoutCodeText.length > 8 ? (
                  <>
                    <span className="text-white/70">{withoutCodeText.slice(0, 8)}</span>
                    <span className="text-white">{withoutCodeText.slice(8)}</span>
                  </>
                ) : (
                  <span className="text-white/70">{withoutCodeText}</span>
                )}
                {withoutCodeText.length < 12 && (
                  <motion.span
                    animate={{ opacity: [1, 0] }}
                    transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
                    className="inline-block w-[3px] h-[0.9em] bg-white/60 ml-1 align-text-bottom"
                  />
                )}
              </span>
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-white/50 max-w-3xl mx-auto">
              Transform your business with intelligent automation. 
              No developers needed. Just drag, drop, and deploy.
            </p>
          </motion.div>

          {/* Workflow Visualization */}
          <div className="relative h-[360px] sm:h-[500px] flex items-center justify-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentCapability}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                {/* Mobile: Vertical flow within workflow */}
                {isMobile && (
                  <div className="absolute inset-x-0 top-[20%] bottom-[20%] left-1/2 -translate-x-1/2 w-0.5 pointer-events-none">
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent"
                      animate={{
                        y: ['-100%', '200%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10" />
                  </div>
                )}
                {/* Capability Title */}
                <div className="text-center mb-8 sm:mb-16">
                  <motion.h2 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-2xl sm:text-3xl md:text-4xl font-bold text-white/80 sm:text-white/90"
                  >
                    {capabilities[currentCapability].title}
                  </motion.h2>
                </div>

                {/* Flow Diagram */}
                <div className="relative flex flex-col sm:flex-row items-center justify-between max-w-5xl mx-auto px-4 sm:px-8 gap-2 sm:gap-0">
                  {capabilities[currentCapability].flow.map((step, index) => (
                    <React.Fragment key={index}>
                      {/* Node */}
                      <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ 
                          scale: activeFlow >= index ? 1 : 0,
                          opacity: activeFlow >= index ? 1 : 0
                        }}
                        transition={{ 
                          duration: 0.5,
                          type: "spring",
                          stiffness: 200
                        }}
                        className="relative"
                      >
                        {/* Active Border Animation */}
                        {activeFlow === index && (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: index === 0 ? 0 : 0.6 }}
                            className="absolute inset-0 rounded-xl overflow-hidden"
                          >
                            {/* Pulsing glow */}
                            <motion.div
                              animate={{
                                opacity: [0.3, 0.6, 0.3],
                                scale: [1, 1.05, 1]
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                              className="absolute inset-0 rounded-xl border border-white/40"
                            />
                            
                            {/* Rotating highlight */}
                            <motion.div
                              animate={{
                                rotate: [0, 360]
                              }}
                              transition={{
                                duration: 4,
                                repeat: Infinity,
                                ease: "linear"
                              }}
                              className="absolute inset-0"
                            >
                              <div 
                                className="absolute inset-0 rounded-xl"
                                style={{
                                  background: 'conic-gradient(from 0deg, transparent 70%, rgba(255,255,255,0.3), transparent 80%)',
                                  maskImage: 'radial-gradient(ellipse at center, transparent 30%, black 70%)'
                                }}
                              />
                            </motion.div>
                          </motion.div>
                        )}
                        
                        {/* Node Content */}
                        <div className={`relative bg-black backdrop-blur-sm border rounded-xl px-4 sm:px-6 py-2.5 sm:py-4 min-w-[120px] sm:min-w-[150px] text-center transition-all duration-300 ${
                          activeFlow === index ? 'border-white/40' : 'border-white/20'
                        }`}>
                          <p className="text-xs sm:text-sm font-medium text-white/80 relative z-10">
                            {step}
                          </p>
                        </div>
                      </motion.div>

                      {/* Connector */}
                      {index < capabilities[currentCapability].flow.length - 1 && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ 
                            scale: activeFlow > index ? 1 : 0
                          }}
                          transition={{ 
                            duration: 0.3,
                            delay: 0.2,
                            ease: "easeOut"
                          }}
                          className="relative h-6 w-0.5 sm:h-px sm:w-auto sm:flex-1 mx-2"
                          style={{
                            transformOrigin: isMobile ? 'top center' : 'left center'
                          }}
                        >
                          {/* Base line */}
                          <div className="absolute inset-0 bg-gradient-to-b sm:bg-gradient-to-r from-white/10 to-white/20" />
                          
                          {/* Flowing light effect */}
                          {activeFlow === index + 1 && (
                            <motion.div
                              className="absolute inset-0 overflow-hidden"
                            >
                              <motion.div
                                initial={isMobile ? { y: '-100%' } : { x: '-100%' }}
                                animate={isMobile ? {
                                  y: ['0%', '200%']
                                } : {
                                  x: ['0%', '200%']
                                }}
                                transition={{
                                  duration: 0.8,
                                  ease: "easeOut",
                                  times: [0, 1]
                                }}
                                className={`absolute ${isMobile ? 'w-full h-12' : 'h-full w-12'}`}
                                style={{
                                  background: isMobile 
                                    ? 'linear-gradient(to bottom, transparent, rgba(255,255,255,0.6), rgba(255,255,255,0.8), rgba(255,255,255,0.6), transparent)'
                                    : 'linear-gradient(to right, transparent, rgba(255,255,255,0.6), rgba(255,255,255,0.8), rgba(255,255,255,0.6), transparent)',
                                }}
                              />
                            </motion.div>
                          )}
                        </motion.div>
                      )}
                    </React.Fragment>
                  ))}
                </div>

                {/* Metric Display */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: activeFlow === 3 ? 1 : 0,
                    y: activeFlow === 3 ? 0 : 20
                  }}
                  transition={{ duration: 0.5 }}
                  className="absolute -bottom-12 sm:-bottom-16 left-0 right-0 text-center"
                >
                  <p className="text-lg sm:text-xl md:text-2xl font-bold text-white/70 sm:text-white/90">
                    {capabilities[currentCapability].metric}
                  </p>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>


          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
            className="absolute bottom-8 left-1/2 -translate-x-1/2"
          >
            <motion.svg 
              animate={{ y: [0, 8, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="w-5 h-5 text-white/20" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </motion.svg>
          </motion.div>
        </motion.div>
      </section>

      {/* Flowing Light Transition - Desktop only */}
      {!isMobile && (
        <div className="relative h-[200px] pointer-events-none overflow-hidden">
          <div className="absolute left-1/2 -translate-x-1/2 w-0.5 h-full">
            <motion.div
              className="absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent"
              animate={{
                y: ['-100%', '200%']
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10" />
          </div>
        </div>
      )}

      {/* How AI Agents Work Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="w-full max-w-7xl mx-auto"
        >
          {/* Section Title */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">
              How AI Agents Work
            </h2>
            <p className="text-lg sm:text-xl text-white/50 max-w-3xl mx-auto">
              Watch AI agents process requests in real-time. No code required.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
            {/* Terminal Simulation */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Terminal Window */}
              <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
                {/* Terminal Header */}
                <div className="flex items-center gap-2 px-4 py-3 border-b border-white/10">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 rounded-full bg-white/20"></div>
                    <div className="w-3 h-3 rounded-full bg-white/20"></div>
                    <div className="w-3 h-3 rounded-full bg-white/20"></div>
                  </div>
                  <span className="text-xs text-white/40 ml-auto">AI Agent Terminal</span>
                </div>
                
                {/* Terminal Content */}
                <div ref={terminalRef} className="p-4 sm:p-6 font-mono text-xs sm:text-sm min-h-[300px] sm:min-h-[400px]">
                  <pre className="text-white/80 whitespace-pre-wrap">
                    {terminalText}
                    {isTyping && <span className="inline-block w-2 h-4 bg-white/60 animate-pulse ml-1" />}
                  </pre>
                </div>
              </div>

              {/* Agent Selector Tabs */}
              <div className="mt-6 flex gap-2 flex-wrap">
                {agentExamples.map((agent, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedAgent(index)}
                    className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 ${
                      selectedAgent === index
                        ? 'bg-white/10 text-white/90 border border-white/20'
                        : 'bg-white/5 text-white/40 border border-white/10 hover:bg-white/10'
                    }`}
                  >
                    {agent.name}
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Architecture Flow Diagram */}
            <motion.div
              initial={{ opacity: 0, x: isMobile ? 0 : 50, y: isMobile ? 50 : 0 }}
              whileInView={{ opacity: 1, x: 0, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="relative mt-8 lg:mt-0"
            >
              {/* Flow Diagram */}
              <div className="space-y-8 relative">
                {/* Animated Flow Line */}
                <div className="absolute top-0 left-1/2 -translate-x-1/2 w-0.5 h-full pointer-events-none">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-b from-transparent via-white/40 to-transparent"
                    animate={{
                      y: ['-100%', '200%']
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-white/10 via-white/5 to-white/10" />
                </div>
                
                {/* User Input */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 0
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">User Input</h3>
                        <p className="text-sm text-white/50">Natural language request</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">1</span>
                    </div>
                  </div>
                </motion.div>

                {/* AI Processing */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 0.5
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center relative">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        {/* Processing Indicator */}
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                          className="absolute inset-0 rounded-lg"
                        >
                          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1 h-1 bg-white/60 rounded-full" />
                        </motion.div>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">AI Processing</h3>
                        <p className="text-sm text-white/50">Understanding intent</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">2</span>
                    </div>
                  </div>
                </motion.div>

                {/* Actions */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 1
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">Actions</h3>
                        <p className="text-sm text-white/50">Automated execution</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">3</span>
                    </div>
                  </div>
                </motion.div>

                {/* Results */}
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="relative z-10"
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-black/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 relative overflow-hidden group">
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    
                    {/* Animated border glow */}
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      style={{
                        background: `linear-gradient(45deg, transparent, white, transparent)`,
                        backgroundSize: '200% 200%',
                      }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: 1.5
                      }}
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 0.1 }}
                      viewport={{ once: true }}
                    />
                    
                    <div className="flex items-center gap-4 relative z-10">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1 text-white/90">Results</h3>
                        <p className="text-sm text-white/50">Value delivered</p>
                      </div>
                    </div>
                    
                    {/* Step indicator */}
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/5 flex items-center justify-center">
                      <span className="text-xs text-white/30">4</span>
                    </div>
                  </div>
                </motion.div>
              </div>
              
              {/* Key Metrics */}
              <div className="grid grid-cols-3 gap-2 sm:gap-4 mt-8 sm:mt-12">
                <motion.div
                  whileHover={{ y: -2 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center"
                >
                  <p className="text-lg sm:text-2xl font-bold text-white/80">100ms</p>
                  <p className="text-[10px] sm:text-xs text-white/40 mt-1">Response Time</p>
                </motion.div>
                <motion.div
                  whileHover={{ y: -2 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center"
                >
                  <p className="text-lg sm:text-2xl font-bold text-white/80">∞</p>
                  <p className="text-[10px] sm:text-xs text-white/40 mt-1">Scale Limit</p>
                </motion.div>
                <motion.div
                  whileHover={{ y: -2 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 text-center"
                >
                  <p className="text-lg sm:text-2xl font-bold text-white/80">0</p>
                  <p className="text-[10px] sm:text-xs text-white/40 mt-1">Code Required</p>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Waitlist Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-5 py-16 sm:py-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="w-full max-w-xl mx-auto text-center"
        >
          {/* Stats */}
          <div className="flex items-center justify-center gap-12 mb-16">
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <p className="text-4xl font-bold text-white/90">24/7</p>
              <p className="text-sm text-white/40">Agent Building</p>
            </motion.div>
            <div className="w-px h-12 bg-white/10" />
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <p className="text-4xl font-bold text-white/90">100%</p>
              <p className="text-sm text-white/40">Done For You</p>
            </motion.div>
          </div>

          {/* Join Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
              Type What You Want
            </h2>
            <p className="text-lg sm:text-xl text-white/50 mb-12">
              Our agents build. You deploy instantly.
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    setError('')
                  }}
                  placeholder="Enter your email"
                  className="w-full px-6 py-4 text-lg bg-white/[0.03] border border-white/10 rounded-xl text-white outline-none transition-all duration-300 placeholder:text-white/30 hover:bg-white/[0.05] focus:bg-white/[0.07] focus:border-white/20"
                  required
                />
                {error && (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="absolute -bottom-6 left-0 text-red-500 text-sm"
                  >
                    {error}
                  </motion.p>
                )}
              </div>

              <button
                type="submit"
                disabled={loading || success}
                className="w-full px-8 py-4 text-lg font-semibold bg-white text-black rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(255,255,255,0.2)] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
              >
                <span className="relative z-10">
                  {loading ? 'Joining...' : success ? 'Welcome aboard!' : 'Get Early Access'}
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
              </button>
            </form>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-3 gap-8 mt-20"
          >
            {[
              { 
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ), 
                title: "Instant Deploy" 
              },
              { 
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                ), 
                title: "Infinite Scale" 
              },
              { 
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                ), 
                title: "Zero Code" 
              }
            ].map((feature, i) => (
              <motion.div 
                key={i} 
                className="text-center"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="inline-flex p-3 rounded-xl bg-white/[0.03] text-white/40 mb-3 group-hover:text-white/60 transition-colors">
                  {feature.icon}
                </div>
                <p className="text-sm text-white/50">{feature.title}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Success Overlay */}
      {success && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/90 backdrop-blur-md flex items-center justify-center z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="bg-white text-black px-8 py-6 rounded-2xl font-semibold text-lg"
          >
            Welcome aboard! Redirecting you to the community...
          </motion.div>
        </motion.div>
      )}
    </main>
  )
}