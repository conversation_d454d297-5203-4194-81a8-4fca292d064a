import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const campaign = searchParams.get('c') || 'qr_default'
    const location = searchParams.get('l') || 'unknown'
    const id = searchParams.get('id') || crypto.randomUUID()
    
    // Generate unique QR tracking data
    const qrData = {
      id,
      campaign,
      location,
      scanned_at: new Date().toISOString(),
      device: request.headers.get('user-agent') || 'unknown'
    }
    
    // Build redirect URL with QR tracking data
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://agentised.com'
    const redirectUrl = new URL(baseUrl)
    redirectUrl.searchParams.set('utm_source', 'qr')
    redirectUrl.searchParams.set('utm_medium', location)
    redirectUrl.searchParams.set('utm_campaign', campaign)
    redirectUrl.searchParams.set('qr_data', Buffer.from(JSON.stringify(qrData)).toString('base64'))
    
    // Track QR scan event (optional - could be done client-side)
    // This would require passing the data to your tracking endpoint
    
    return NextResponse.redirect(redirectUrl.toString())
  } catch (error) {
    console.error('QR redirect error:', error)
    return NextResponse.redirect(process.env.NEXT_PUBLIC_BASE_URL || 'https://agentised.com')
  }
}

// Generate QR code endpoint
export async function POST(request: Request) {
  try {
    const { campaign = 'default', location = 'web' } = await request.json()
    
    // Generate unique ID for this QR code
    const qrId = crypto.randomUUID()
    
    // Build QR code URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://agentised.com'
    const qrUrl = `${baseUrl}/api/qr?c=${encodeURIComponent(campaign)}&l=${encodeURIComponent(location)}&id=${qrId}`
    
    // You can use a QR code generation service or library
    // For example, using qr-server.com (free service)
    const qrCodeImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrUrl)}`
    
    return NextResponse.json({
      qr_id: qrId,
      qr_url: qrUrl,
      qr_image: qrCodeImageUrl,
      campaign,
      location
    })
  } catch (error) {
    console.error('QR generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate QR code' },
      { status: 500 }
    )
  }
}