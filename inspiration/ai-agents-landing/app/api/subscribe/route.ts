import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    // Validate email
    if (!email || !isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      )
    }

    // Here you would typically:
    // 1. Save to database (e.g., MongoDB, PostgreSQL, Supabase)
    // 2. Add to email service (e.g., SendGrid, Mailchimp, ConvertKit)
    // 3. Send welcome email
    
    // For now, we'll just log it
    console.log('New subscriber:', email)
    
    // Example: Add to email service
    // await addToMailingList(email)
    
    // Example: Save to database
    // await db.subscribers.create({ email, subscribedAt: new Date() })

    return NextResponse.json(
      { message: 'Successfully subscribed!' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Subscription error:', error)
    return NextResponse.json(
      { error: 'Failed to subscribe' },
      { status: 500 }
    )
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Example function to add to mailing list
// async function addToMailingList(email: string) {
//   const response = await fetch('https://api.sendgrid.com/v3/contacts', {
//     method: 'PUT',
//     headers: {
//       'Authorization': `Bearer ${process.env.SENDGRID_API_KEY}`,
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({
//       contacts: [{ email }],
//       list_ids: [process.env.SENDGRID_LIST_ID],
//     }),
//   })
//   
//   if (!response.ok) {
//     throw new Error('Failed to add to mailing list')
//   }
// }