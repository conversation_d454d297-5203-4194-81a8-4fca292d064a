import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';

// Path to the agentcli executable
const AGENT_CLI_PATH = path.join(process.cwd(), '..', 'agentcli', 'packages', 'cli', 'dist', 'gemini.js');

export async function POST(request: NextRequest) {
  try {
    const { prompt, template, userId } = await request.json();

    if (!prompt || !template) {
      return NextResponse.json(
        { error: 'Prompt and template are required' },
        { status: 400 }
      );
    }

    // Create a session ID
    const sessionId = `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Store session info (in production, use a database)
    const session = {
      id: sessionId,
      userId,
      template,
      prompt,
      status: 'initializing',
      createdAt: new Date()
    };

    return NextResponse.json({
      sessionId,
      message: 'Agent session created. Connect via WebSocket for real-time updates.'
    });

  } catch (error) {
    console.error('Error creating agent session:', error);
    return NextResponse.json(
      { error: 'Failed to create agent session' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const userId = request.headers.get('x-user-id');
  
  // In production, fetch from database
  const mockAgents = [
    {
      id: 'agent_1',
      name: 'Customer Support Bot',
      template: 'customer-support',
      status: 'active',
      createdAt: new Date('2024-01-15'),
      metrics: {
        messagesHandled: 1234,
        avgResponseTime: '2.3s',
        satisfaction: 4.8
      }
    },
    {
      id: 'agent_2',
      name: 'Sales Assistant',
      template: 'sales-automation',
      status: 'active',
      createdAt: new Date('2024-01-20'),
      metrics: {
        leadsProcessed: 567,
        meetingsScheduled: 89,
        conversionRate: '15.2%'
      }
    }
  ];

  return NextResponse.json({ agents: mockAgents });
}