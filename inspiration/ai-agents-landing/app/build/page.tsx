'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import io, { Socket } from 'socket.io-client';
import AgentConsole from '@/components/AgentConsole';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

interface ConsoleLine {
  type: 'command' | 'output' | 'error' | 'success' | 'thinking';
  content: string;
  timestamp: Date;
}

const AGENT_TYPES = [
  { 
    id: 'customer-support', 
    name: 'Customer Support', 
    icon: '💬',
    description: 'Handles inquiries, resolves issues, provides 24/7 support'
  },
  { 
    id: 'sales-automation', 
    name: 'Sales Automation', 
    icon: '📈',
    description: 'Qualifies leads, schedules meetings, manages follow-ups'
  },
  { 
    id: 'content-creation', 
    name: 'Content Creation', 
    icon: '✍️',
    description: 'Generates content, maintains brand voice, SEO optimization'
  },
  { 
    id: 'data-analysis', 
    name: 'Data Analysis', 
    icon: '📊',
    description: 'Analyzes metrics, creates reports, provides insights'
  }
];

export default function BuildInterface() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [consoleLines, setConsoleLines] = useState<ConsoleLine[]>([]);
  const [input, setInput] = useState('');
  const [socket, setSocket] = useState<Socket | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [agentName, setAgentName] = useState('');
  const [isBuilding, setIsBuilding] = useState(false);
  const [showConsole, setShowConsole] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const socketInstance = io('http://localhost:3001');
    setSocket(socketInstance);
    setSessionId(`session_${Date.now()}`);

    socketInstance.on('connect', () => {
      console.log('Connected to agent builder');
    });

    socketInstance.on('agent-output', (data) => {
      setConsoleLines(prev => [...prev, {
        type: 'output',
        content: data.output.trim(),
        timestamp: new Date()
      }]);
    });

    socketInstance.on('agent-thinking', (data) => {
      setConsoleLines(prev => [...prev, {
        type: 'thinking',
        content: data.thought.trim(),
        timestamp: new Date()
      }]);
    });

    socketInstance.on('tool-execution', (data) => {
      setConsoleLines(prev => [...prev, {
        type: 'command',
        content: `Executing ${data.toolName}...`,
        timestamp: new Date()
      }]);
    });

    socketInstance.on('build-progress', (data) => {
      if (data.status === 'completed') {
        setIsBuilding(false);
        setConsoleLines(prev => [...prev, {
          type: 'success',
          content: 'Agent created successfully!',
          timestamp: new Date()
        }]);
        
        setMessages(prev => [...prev, {
          id: Date.now().toString(),
          role: 'assistant',
          content: `✅ Your ${AGENT_TYPES.find(a => a.id === selectedAgent)?.name} agent "${agentName}" has been created!\n\nYou can now start using it or create another agent.`,
          timestamp: new Date()
        }]);
      }
    });

    socketInstance.on('agent-error', (data) => {
      setConsoleLines(prev => [...prev, {
        type: 'error',
        content: data.error,
        timestamp: new Date()
      }]);
      setIsBuilding(false);
    });

    return () => {
      socketInstance.disconnect();
    };
  }, [selectedAgent, agentName]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleAgentSelect = (agentId: string) => {
    setSelectedAgent(agentId);
    setShowConsole(true);
    
    const agent = AGENT_TYPES.find(a => a.id === agentId);
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      role: 'assistant',
      content: `Let's create a ${agent?.name} agent! ${agent?.icon}\n\nFirst, give your agent a name:`,
      timestamp: new Date()
    }]);
  };

  const handleSend = () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    if (!agentName) {
      setAgentName(input);
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        role: 'assistant',
        content: `Great! "${input}" is a perfect name.\n\nNow describe what specific tasks you want ${input} to handle:`,
        timestamp: new Date()
      }]);
    } else if (!isBuilding) {
      startBuilding(input);
    }

    setInput('');
  };

  const startBuilding = (prompt: string) => {
    setIsBuilding(true);
    
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      role: 'assistant',
      content: `🚀 Building "${agentName}"...\n\nWatch the console to see the build progress in real-time.`,
      timestamp: new Date()
    }]);

    setConsoleLines([{
      type: 'command',
      content: `Initializing ${agentName}...`,
      timestamp: new Date()
    }]);

    socket?.emit('start-agent', {
      sessionId,
      prompt: `Create an agent named "${agentName}" that: ${prompt}`,
      template: selectedAgent
    });
  };

  return (
    <div className="min-h-screen bg-black text-white flex">
      {/* Sidebar */}
      <div className="w-80 border-r border-white/10 bg-black/50 flex flex-col">
        <div className="p-4 border-b border-white/10">
          <Link href="/" className="text-xl font-bold">AI Agent Builder</Link>
        </div>
        
        <div className="flex-1 p-4 overflow-y-auto">
          <h2 className="text-sm text-white/60 uppercase tracking-wider mb-4">Agent Templates</h2>
          <div className="space-y-2">
            {AGENT_TYPES.map((agent) => (
              <motion.button
                key={agent.id}
                onClick={() => handleAgentSelect(agent.id)}
                whileHover={{ x: 4 }}
                className={`w-full p-3 rounded-lg text-left transition-all ${
                  selectedAgent === agent.id 
                    ? 'bg-white/10 border border-white/20' 
                    : 'hover:bg-white/5'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">{agent.icon}</span>
                  <div className="flex-1">
                    <h3 className="font-medium">{agent.name}</h3>
                    <p className="text-xs text-white/50 mt-1">{agent.description}</p>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        <div className="p-4 border-t border-white/10">
          <button
            onClick={() => window.location.reload()}
            className="w-full px-4 py-2 bg-white/5 rounded-lg hover:bg-white/10 transition-colors text-sm"
          >
            New Session
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto p-6">
            {messages.length === 0 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-20"
              >
                <h1 className="text-4xl font-bold mb-4">Build Your AI Agent</h1>
                <p className="text-xl text-white/60">Select a template from the sidebar to get started</p>
              </motion.div>
            )}

            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-6"
                >
                  {message.role === 'user' && (
                    <div className="flex justify-end">
                      <div className="max-w-md bg-white/10 rounded-2xl px-4 py-3">
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      </div>
                    </div>
                  )}

                  {message.role === 'assistant' && (
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-xs">AI</span>
                      </div>
                      <div className="max-w-md">
                        <p className="text-white/90 whitespace-pre-wrap">{message.content}</p>
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t border-white/10 p-4">
            <div className="flex items-center space-x-3">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSend();
                  }
                }}
                placeholder={
                  !selectedAgent 
                    ? "Select an agent template to start..." 
                    : isBuilding 
                    ? "Agent is building..." 
                    : !agentName 
                    ? "Enter agent name..." 
                    : "Describe what your agent should do..."
                }
                disabled={!selectedAgent || isBuilding}
                className="flex-1 bg-white/5 border border-white/20 rounded-lg px-4 py-3 focus:outline-none focus:border-white/40 disabled:opacity-50"
              />
              <button
                onClick={handleSend}
                disabled={!input.trim() || !selectedAgent || isBuilding}
                className="px-6 py-3 bg-white text-black rounded-lg font-medium hover:bg-white/90 transition-colors disabled:opacity-50"
              >
                Send
              </button>
            </div>
          </div>
        </div>

        {/* Console */}
        {showConsole && (
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: 480 }}
            className="border-l border-white/10 bg-black/30 p-6"
          >
            <AgentConsole lines={consoleLines} isBuilding={isBuilding} />
          </motion.div>
        )}
      </div>
    </div>
  );
}