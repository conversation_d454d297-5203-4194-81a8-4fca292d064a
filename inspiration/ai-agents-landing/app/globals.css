@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply antialiased;
    background-color: #000000;
    /* Handle safe areas for notched iPhones */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Full viewport height accounting for Safari UI */
  html, body {
    height: 100%;
    overflow-x: hidden;
  }
  
  /* Use dynamic viewport height for mobile */
  @supports (height: 100dvh) {
    body {
      min-height: 100dvh;
    }
  }
  
  /* Prevent text selection on mobile for a cleaner experience */
  @media (max-width: 768px) {
    body {
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
      /* Extend content to edges */
      padding-left: env(safe-area-inset-left);
      padding-right: env(safe-area-inset-right);
    }
  }
  
  /* Improve touch targets */
  button, a, input, textarea {
    @apply min-h-[44px];
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  .animation-delay-600 {
    animation-delay: 600ms;
  }
}