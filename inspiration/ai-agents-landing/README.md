# AI Agents Platform

This platform allows businesses to create AI agents without coding, using the power of Google's Gemini CLI (agentcli) under the hood.

## Architecture Overview

```
┌─────────────────────┐         ┌──────────────────┐
│   Next.js Frontend  │ ◄─────► │  WebSocket Server│
│   - Landing Page    │         │  (Socket.io)     │
│   - Agent Dashboard │         └────────┬─────────┘
│   - Builder UI      │                  │
└─────────────────────┘                  │
                                         ▼
                              ┌──────────────────┐
                              │    agentcli      │
                              │  (Gemini CLI)    │
                              │  - AI Processing │
                              │  - Tool Execution│
                              └──────────────────┘
```

## Setup Instructions

### Prerequisites

1. Node.js 20+ installed
2. Google Gemini API key (get from https://makersuite.google.com/app/apikey)
3. Both `/ai-agents-landing` and `/agentcli` directories in place

### Step 1: Build agentcli

```bash
cd ../agentcli
npm install
npm run build
```

### Step 2: Configure Environment

Create `.env.local` in the ai-agents-landing directory:

```env
GEMINI_API_KEY=your_gemini_api_key_here
NEXT_PUBLIC_APP_URL=http://localhost:3000
WEBSOCKET_PORT=3001
```

### Step 3: Install Dependencies

```bash
npm install
```

### Step 4: Run the Platform

Option 1 - Run everything together:
```bash
npm run dev:all
```

Option 2 - Run separately (recommended for development):

Terminal 1 - Next.js Frontend:
```bash
npm run dev
```

Terminal 2 - WebSocket Server:
```bash
npm run websocket
```

### Step 5: Access the Platform

1. Landing Page: http://localhost:3000
2. Agent Builder (Cursor-like interface): http://localhost:3000/build
3. Chat Interface: http://localhost:3000/chat

## Features

### Agent Templates

1. **Customer Support Agent**
   - Handles customer inquiries
   - Provides 24/7 support
   - Escalates complex issues

2. **Sales Automation Agent**
   - Qualifies leads
   - Schedules meetings
   - Manages follow-ups

3. **Content Creation Agent**
   - Generates blog posts
   - Creates social media content
   - SEO optimization

4. **Data Analysis Agent**
   - Analyzes metrics
   - Creates reports
   - Provides insights

### How It Works

1. **User selects a template** in the dashboard
2. **Provides instructions** for what the agent should do
3. **WebSocket server** spawns an agentcli process
4. **Real-time updates** show the agent building process
5. **Agent is deployed** and ready to use

### API Endpoints

- `POST /api/agent` - Create new agent
- `GET /api/agent` - List user's agents
- `POST /api/agent/execute` - Execute agent task
- `GET /api/websocket` - WebSocket connection info

### WebSocket Events

Client → Server:
- `start-agent` - Start building an agent
- `send-message` - Send message to agent
- `stop-agent` - Stop agent execution

Server → Client:
- `build-progress` - Building progress updates
- `agent-thinking` - Agent thought process
- `tool-execution` - Tool execution status
- `agent-output` - Agent responses
- `agent-complete` - Build completion

## Development Notes

- The platform wraps agentcli without modifying its core
- Each agent session spawns a separate agentcli process
- WebSocket provides real-time communication
- Templates add business-specific context to prompts

## Production Deployment

For production:
1. Use proper authentication (add auth middleware)
2. Set up database for agent storage
3. Configure rate limiting
4. Use PM2 or similar for process management
5. Set up proper logging and monitoring

## Troubleshooting

1. **agentcli not found**: Make sure to build agentcli first
2. **WebSocket connection failed**: Check if port 3001 is available
3. **No Gemini API key**: Set GEMINI_API_KEY in .env.local
4. **Build errors**: Ensure Node.js 20+ is installed