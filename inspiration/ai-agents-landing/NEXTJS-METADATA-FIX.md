# Next.js 15 Metadata Export Error Fix

## The Problem
Next.js 15 introduced breaking changes to how metadata is exported. The error "Unsupported metadata viewport/themeColor is configured in metadata export" occurs because these properties were moved to a separate export.

## What Caused This
In Next.js 14 and earlier, you could include `viewport` and `themeColor` directly in the metadata export:
```typescript
export const metadata: Metadata = {
  viewport: {...},  // ❌ NO LONGER SUPPORTED
  themeColor: '#000000',  // ❌ NO LONGER SUPPORTED
}
```

## The Solution
Next.js 15 requires these to be in a separate `viewport` export:
```typescript
export const metadata: Metadata = {
  title: 'Your Title',
  description: 'Your Description',
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#000000',  // ✅ NOW GOES HERE
}
```

## How to Prevent This Forever
1. **Always check Next.js migration guides** when upgrading versions
2. **Use the viewport export** for viewport-related configurations
3. **Keep metadata export** for title, description, and other non-viewport metadata
4. **Run the dev server immediately** after upgrading to catch these errors early

## Quick Reference
- `metadata` export: title, description, openGraph, twitter, etc.
- `viewport` export: width, initialScale, themeColor, viewportFit, etc.

This separation makes the codebase cleaner and follows Next.js 15's new architecture.