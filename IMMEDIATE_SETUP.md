# 🚀 IMMEDIATE SETUP - Copy & Paste These Commands

## Step 1: Start the App (Run these commands NOW)

```bash
cd "/Applications/AI Project /agentised/inspiration/ai-agents-landing"
npm install
npm run dev
```

Your app should now be running at: **http://localhost:3000**

## Step 2: Get Your Supabase Keys

**IMPORTANT**: The waitlist won't work until you add your Supabase keys!

1. Go to: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api
2. Copy your keys:
   - **anon public** key (starts with `eyJ...`)
   - **service_role** key (keep this secret!)

3. Update your `.env.local` file:
```bash
# Open the file
open "/Applications/AI Project /agentised/inspiration/ai-agents-landing/.env.local"
```

Replace:
- `your-anon-key-here` → with your anon key
- `your-service-role-key-here` → with your service_role key
- `your-secure-admin-key-here` → with any random string (e.g., `admin123secret`)

## Step 3: Apply Database Migration

Go to: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/sql/new

Copy and paste the entire contents of:
`/Applications/AI Project /agentised/supabase/migrations/001_waitlist_schema.sql`

Click "Run" to create all tables.

## Step 4: Deploy Edge Functions (Optional for now)

The waitlist will work without this step, but for full functionality:

```bash
# Install Supabase CLI
brew install supabase/tap/supabase

# Login and link
supabase login
supabase link --project-ref uqohigtauhgygiyplftk

# Deploy functions
cd "/Applications/AI Project /agentised"
supabase functions deploy waitlist-join --no-verify-jwt
supabase functions deploy waitlist-status --no-verify-jwt
supabase functions deploy waitlist-analytics --no-verify-jwt
```

## 🎉 That's it! Test your waitlist:

1. Go to http://localhost:3000
2. Enter an email and submit
3. Check Supabase dashboard: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/editor

You should see the email in the `waitlist_users` table!

## Troubleshooting

**Port already in use?**
```bash
PORT=3001 npm run dev
```

**Dependencies issue?**
```bash
rm -rf node_modules package-lock.json
npm install
```

**Can't find Supabase keys?**
The keys are in: Settings → API → Project API keys