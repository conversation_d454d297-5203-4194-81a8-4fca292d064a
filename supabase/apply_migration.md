# Apply Database Migration to Supabase

Since the MCP connection is unstable, please apply the migration manually:

## Option 1: Supabase Dashboard (Recommended)

1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/uqohigtauhgygiyplftk
2. Navigate to the SQL Editor
3. Copy and paste the contents of `/supabase/migrations/001_waitlist_schema.sql`
4. Click "Run" to execute the migration

## Option 2: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Install Supabase CLI if needed
brew install supabase/tap/supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref uqohigtauhgygiyplftk

# Apply the migration
supabase db push
```

## After Migration is Applied

Once the migration is successfully applied, we need to:
1. Get the project credentials (SUPABASE_URL and SUPABASE_ANON_KEY)
2. Deploy the Edge Functions
3. Update the frontend integration

## Get Credentials

After applying the migration, get your credentials from:
https://supabase.com/dashboard/project/uqohigtauhgygiyplftk/settings/api

- SUPABASE_URL: `https://uqohigtauhgygiyplftk.supabase.co`
- SUPABASE_ANON_KEY: (Copy from dashboard)