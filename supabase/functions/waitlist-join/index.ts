import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface WaitlistRequest {
  email: string
  name?: string
  company?: string
  role?: string
  interests?: string[]
  source?: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  referred_by_code?: string
  qr_code_data?: any
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { email, name, company, role, interests, source, utm_source, utm_medium, utm_campaign, referred_by_code, qr_code_data } = await req.json() as WaitlistRequest

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email || !emailRegex.test(email)) {
      return new Response(
        JSON.stringify({ error: 'Invalid email address' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400 
        }
      )
    }

    // Get IP and user agent
    const ip_address = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
    const user_agent = req.headers.get('user-agent')

    // Handle referral
    let referred_by = null
    if (referred_by_code) {
      const { data: referrer } = await supabaseClient
        .from('waitlist_users')
        .select('id')
        .eq('referral_code', referred_by_code.toUpperCase())
        .single()
      
      if (referrer) {
        referred_by = referrer.id
      }
    }

    // Build metadata
    const metadata: any = {}
    if (qr_code_data) {
      metadata.qr_code_data = qr_code_data
    }

    // Insert user
    const { data: user, error: insertError } = await supabaseClient
      .from('waitlist_users')
      .insert({
        email,
        name,
        company,
        role,
        interests,
        source: source || 'website',
        utm_source,
        utm_medium,
        utm_campaign,
        referred_by,
        ip_address,
        user_agent,
        metadata: Object.keys(metadata).length > 0 ? metadata : undefined
      })
      .select()
      .single()

    if (insertError) {
      // Check if email already exists
      if (insertError.code === '23505') {
        return new Response(
          JSON.stringify({ error: 'Email already registered' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 409 
          }
        )
      }
      throw insertError
    }

    // Track signup event
    await supabaseClient.rpc('track_engagement', {
      p_user_id: user.id,
      p_event_type: 'signup_completed',
      p_event_data: {
        source: source || 'website',
        referred: !!referred_by
      },
      p_ip_address: ip_address
    })

    // Create referral conversion if applicable
    if (referred_by) {
      await supabaseClient
        .from('referral_conversions')
        .insert({
          referrer_id: referred_by,
          referred_id: user.id
        })
    }

    return new Response(
      JSON.stringify({
        message: 'Successfully joined waitlist!',
        position: user.position,
        referral_code: user.referral_code
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error in waitlist-join:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to join waitlist' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})