import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface AnalyticsRequest {
  admin_key?: string
  date_from?: string
  date_to?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Use service role key for admin access
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          persistSession: false
        }
      }
    )

    const { admin_key, date_from, date_to } = await req.json() as AnalyticsRequest

    // Simple admin authentication - replace with proper auth in production
    if (admin_key !== Deno.env.get('ADMIN_KEY')) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401 
        }
      )
    }

    // Get overall stats
    const { data: overallStats } = await supabaseClient
      .from('waitlist_users')
      .select('status')
      .single()
      .then(() => supabaseClient.rpc('get_waitlist_stats'))

    // Get user growth over time
    let analyticsQuery = supabaseClient
      .from('waitlist_analytics')
      .select('*')
      .order('signup_date', { ascending: false })

    if (date_from) {
      analyticsQuery = analyticsQuery.gte('signup_date', date_from)
    }
    if (date_to) {
      analyticsQuery = analyticsQuery.lte('signup_date', date_to)
    }

    const { data: dailyStats } = await analyticsQuery

    // Get top referrers
    const { data: topReferrers } = await supabaseClient
      .from('waitlist_users')
      .select(`
        id,
        email,
        referral_code,
        referral_conversions!referrer_id(count)
      `)
      .not('referral_conversions', 'is', null)
      .order('referral_conversions.count', { ascending: false })
      .limit(10)

    // Get source distribution
    const { data: sourceStats } = await supabaseClient
      .from('waitlist_users')
      .select('source')
      .then(result => {
        const sources = result.data?.reduce((acc: any, user: any) => {
          acc[user.source] = (acc[user.source] || 0) + 1
          return acc
        }, {})
        return { data: sources }
      })

    // Get recent signups
    const { data: recentSignups } = await supabaseClient
      .from('waitlist_users')
      .select('email, source, created_at, referred_by')
      .order('created_at', { ascending: false })
      .limit(20)

    // Get engagement metrics
    const { data: engagementStats } = await supabaseClient
      .from('engagement_events')
      .select('event_type')
      .then(result => {
        const events = result.data?.reduce((acc: any, event: any) => {
          acc[event.event_type] = (acc[event.event_type] || 0) + 1
          return acc
        }, {})
        return { data: events }
      })

    return new Response(
      JSON.stringify({
        overall: overallStats,
        daily_stats: dailyStats,
        top_referrers: topReferrers,
        source_distribution: sourceStats,
        recent_signups: recentSignups,
        engagement_metrics: engagementStats
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error in waitlist-analytics:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to get analytics' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

// Helper RPC function to add to your database
const getWaitlistStatsFunction = `
CREATE OR REPLACE FUNCTION get_waitlist_stats()
RETURNS TABLE (
  total_users BIGINT,
  waiting_users BIGINT,
  invited_users BIGINT,
  converted_users BIGINT,
  total_referrals BIGINT,
  avg_referrals_per_user NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::BIGINT as total_users,
    COUNT(*) FILTER (WHERE status = 'waiting')::BIGINT as waiting_users,
    COUNT(*) FILTER (WHERE status = 'invited')::BIGINT as invited_users,
    COUNT(*) FILTER (WHERE status = 'converted')::BIGINT as converted_users,
    (SELECT COUNT(*) FROM referral_conversions)::BIGINT as total_referrals,
    COALESCE(
      (SELECT COUNT(*)::NUMERIC / NULLIF(COUNT(DISTINCT referrer_id), 0) 
       FROM referral_conversions), 
      0
    ) as avg_referrals_per_user
  FROM waitlist_users;
END;
$$ LANGUAGE plpgsql;
`