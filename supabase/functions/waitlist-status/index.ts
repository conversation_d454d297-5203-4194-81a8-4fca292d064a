import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface StatusRequest {
  email?: string
  referral_code?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { email, referral_code } = await req.json() as StatusRequest

    if (!email && !referral_code) {
      return new Response(
        JSON.stringify({ error: 'Email or referral code required' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400 
        }
      )
    }

    // Build query
    let query = supabaseClient
      .from('waitlist_users')
      .select('id, email, position, status, referral_code, created_at')

    if (email) {
      query = query.eq('email', email)
    } else if (referral_code) {
      query = query.eq('referral_code', referral_code.toUpperCase())
    }

    const { data: user, error: fetchError } = await query.single()

    if (fetchError || !user) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404 
        }
      )
    }

    // Get total waitlist count
    const { count: totalCount } = await supabaseClient
      .from('waitlist_users')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'waiting')

    // Get referral stats if checking by referral code
    let referralStats = null
    if (referral_code) {
      const { data: referrals } = await supabaseClient
        .from('referral_conversions')
        .select('id')
        .eq('referrer_id', user.id)

      referralStats = {
        total_referrals: referrals?.length || 0,
        referral_link: `${req.headers.get('origin') || 'https://agentised.com'}?ref=${user.referral_code}`
      }
    }

    // Track status check event
    await supabaseClient.rpc('track_engagement', {
      p_user_id: user.id,
      p_event_type: 'status_checked',
      p_event_data: {
        method: email ? 'email' : 'referral_code'
      },
      p_ip_address: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
    })

    return new Response(
      JSON.stringify({
        position: user.position,
        status: user.status,
        total_in_waitlist: totalCount || 0,
        joined_at: user.created_at,
        referral_code: user.referral_code,
        ...referralStats && { referral_stats: referralStats }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error in waitlist-status:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to get status' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})