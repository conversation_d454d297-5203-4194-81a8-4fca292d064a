-- Create waitlist users table
CREATE TABLE waitlist_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255),
  company VARCHAR(255),
  role VARCHAR(255),
  interests TEXT[],
  source VARCHAR(100) DEFAULT 'website',
  utm_source VARCHAR(100),
  utm_medium VARCHAR(100),
  utm_campaign VARCHAR(100),
  referral_code VARCHAR(50) UNIQUE DEFAULT UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8)),
  referred_by UUID REFERENCES waitlist_users(id),
  position INTEGER,
  status VARCHAR(50) DEFAULT 'waiting', -- waiting, invited, converted, churned
  ip_address INET,
  user_agent TEXT,
  country VARCHAR(2),
  city VARCHAR(100),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create engagement events table
CREATE TABLE engagement_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES waitlist_users(id) ON DELETE CASCADE,
  event_type VARCHAR(100) NOT NULL, -- page_view, email_open, email_click, referral_click, signup_started, etc
  event_data JSONB DEFAULT '{}',
  session_id VARCHAR(100),
  page_url TEXT,
  referrer_url TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create email events table
CREATE TABLE email_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES waitlist_users(id) ON DELETE CASCADE,
  email_type VARCHAR(100) NOT NULL, -- welcome, weekly_update, milestone, invitation
  subject TEXT,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  unsubscribed_at TIMESTAMP WITH TIME ZONE,
  bounced_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}'
);

-- Create referral tracking table
CREATE TABLE referral_conversions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  referrer_id UUID REFERENCES waitlist_users(id),
  referred_id UUID REFERENCES waitlist_users(id),
  reward_given BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_waitlist_users_email ON waitlist_users(email);
CREATE INDEX idx_waitlist_users_referral_code ON waitlist_users(referral_code);
CREATE INDEX idx_waitlist_users_status ON waitlist_users(status);
CREATE INDEX idx_waitlist_users_position ON waitlist_users(position);
CREATE INDEX idx_engagement_events_user_id ON engagement_events(user_id);
CREATE INDEX idx_engagement_events_type ON engagement_events(event_type);
CREATE INDEX idx_engagement_events_created ON engagement_events(created_at);
CREATE INDEX idx_email_events_user_id ON email_events(user_id);

-- Create function to auto-update position
CREATE OR REPLACE FUNCTION update_waitlist_position()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.position IS NULL THEN
    NEW.position := (SELECT COUNT(*) + 1 FROM waitlist_users WHERE status = 'waiting');
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for auto-position
CREATE TRIGGER set_waitlist_position
BEFORE INSERT ON waitlist_users
FOR EACH ROW
EXECUTE FUNCTION update_waitlist_position();

-- Create function to track engagement
CREATE OR REPLACE FUNCTION track_engagement(
  p_user_id UUID,
  p_event_type VARCHAR,
  p_event_data JSONB DEFAULT '{}',
  p_session_id VARCHAR DEFAULT NULL,
  p_page_url TEXT DEFAULT NULL,
  p_ip_address INET DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_event_id UUID;
BEGIN
  INSERT INTO engagement_events (
    user_id,
    event_type,
    event_data,
    session_id,
    page_url,
    ip_address
  ) VALUES (
    p_user_id,
    p_event_type,
    p_event_data,
    p_session_id,
    p_page_url,
    p_ip_address
  ) RETURNING id INTO v_event_id;
  
  -- Update user's last activity
  UPDATE waitlist_users 
  SET updated_at = NOW() 
  WHERE id = p_user_id;
  
  RETURN v_event_id;
END;
$$ LANGUAGE plpgsql;

-- Create view for analytics
CREATE VIEW waitlist_analytics AS
SELECT
  COUNT(DISTINCT wu.id) as total_users,
  COUNT(DISTINCT CASE WHEN wu.status = 'waiting' THEN wu.id END) as waiting_users,
  COUNT(DISTINCT CASE WHEN wu.status = 'invited' THEN wu.id END) as invited_users,
  COUNT(DISTINCT CASE WHEN wu.status = 'converted' THEN wu.id END) as converted_users,
  COUNT(DISTINCT wu.referred_by) as total_referrers,
  COUNT(DISTINCT rc.referred_id) as total_referred,
  DATE(wu.created_at) as signup_date
FROM waitlist_users wu
LEFT JOIN referral_conversions rc ON wu.id = rc.referrer_id
GROUP BY DATE(wu.created_at);

-- Create RLS policies
ALTER TABLE waitlist_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE engagement_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_events ENABLE ROW LEVEL SECURITY;

-- Public can insert into waitlist
CREATE POLICY "Public can join waitlist" ON waitlist_users
  FOR INSERT WITH CHECK (true);

-- Users can view their own data
CREATE POLICY "Users can view own data" ON waitlist_users
  FOR SELECT USING (auth.uid()::text = id::text OR true); -- Adjust based on your auth setup