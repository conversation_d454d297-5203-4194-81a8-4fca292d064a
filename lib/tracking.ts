import { v4 as uuidv4 } from 'uuid'

// Get or create session ID
export function getSessionId(): string {
  if (typeof window === 'undefined') return ''
  
  let sessionId = sessionStorage.getItem('waitlist_session_id')
  if (!sessionId) {
    sessionId = uuidv4()
    sessionStorage.setItem('waitlist_session_id', sessionId)
  }
  return sessionId
}

// Track engagement events
export async function trackEvent(
  eventType: string,
  eventData: Record<string, any> = {},
  userId?: string
) {
  try {
    const sessionId = getSessionId()
    
    await fetch('/api/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        eventType,
        eventData,
        sessionId,
        pageUrl: window.location.href,
        referrerUrl: document.referrer
      })
    })
  } catch (error) {
    console.error('Failed to track event:', error)
  }
}

// Track page views
export function trackPageView(userId?: string) {
  trackEvent('page_view', {
    path: window.location.pathname,
    search: window.location.search,
    hash: window.location.hash
  }, userId)
}

// Track form interactions
export function trackFormInteraction(formName: string, action: string, userId?: string) {
  trackEvent('form_interaction', {
    form_name: formName,
    action, // started, abandoned, completed
    timestamp: new Date().toISOString()
  }, userId)
}

// Track referral clicks
export function trackReferralClick(referralCode: string) {
  trackEvent('referral_click', {
    referralCode,
    landing_page: window.location.pathname
  })
}

// Initialize tracking on page load
export function initializeTracking(userId?: string) {
  // Track page view
  trackPageView(userId)
  
  // Track time on page
  let startTime = Date.now()
  window.addEventListener('beforeunload', () => {
    const timeOnPage = Math.round((Date.now() - startTime) / 1000)
    trackEvent('page_exit', {
      time_on_page: timeOnPage,
      path: window.location.pathname
    }, userId)
  })
  
  // Track scroll depth
  let maxScroll = 0
  let ticking = false
  
  function updateMaxScroll() {
    const scrollPercent = Math.round(
      (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
    )
    if (scrollPercent > maxScroll) {
      maxScroll = scrollPercent
      if (maxScroll % 25 === 0) { // Track at 25%, 50%, 75%, 100%
        trackEvent('scroll_depth', {
          depth: maxScroll,
          path: window.location.pathname
        }, userId)
      }
    }
    ticking = false
  }
  
  window.addEventListener('scroll', () => {
    if (!ticking) {
      window.requestAnimationFrame(updateMaxScroll)
      ticking = true
    }
  })
}

// Email tracking helpers
export function getEmailTrackingPixel(emailId: string, userId: string): string {
  return `${process.env.NEXT_PUBLIC_BASE_URL}/api/track?eid=${emailId}&uid=${userId}`
}

export function getTrackedLink(url: string, emailId: string, linkId: string): string {
  const trackingUrl = new URL(`${process.env.NEXT_PUBLIC_BASE_URL}/api/track/click`)
  trackingUrl.searchParams.set('url', url)
  trackingUrl.searchParams.set('eid', emailId)
  trackingUrl.searchParams.set('lid', linkId)
  return trackingUrl.toString()
}